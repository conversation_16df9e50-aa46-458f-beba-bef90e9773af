﻿---
title: Index
description: 
---

# Index
---
title: Python 基础知识
description: 本文档汇总 Python 编程的基础概念和常用操作，旨在为初学者提供一个快速入门的指引。
---

# Python 基础知识

本文档汇总 Python 编程的基础概念和常用操作，旨在为初学者提供一个快速入门的指引，并为有经验的开发者提供一个便捷的参考。

Python 是一种解释型、面向对象、动态数据类型的高级程序设计语言。由于其简洁的语法和强大的库支持，Python 在数据科学、机器学习、Web 开发、自动化脚本等多个领域都有广泛应用。

以下各章节将分别介绍 Python 的核心数据结构和基本概念：

-   **[Python 基础知识字典](./python-basic-dictionary.md)**：一个快速查阅 Python 核心语法和内置功能的参考手册。
-   **[数组 (Arrays)](./data-structures/arrays.md)**：虽然 Python 内置类型中没有像 C++/Java 中那样的原生数组，但列表 (list) 提供了类似的功能，本节也会探讨如何用列表模拟数组以及多维数组的概念。
-   **[字符串 (Strings)](./data-structures/strings.md)**：介绍 Python 中字符串的创建、操作和常用方法。
-   **[队列 (Queues)](./data-structures/queues.md)**：讲解队列的 FIFO (先进先出) 原理及其 Python 实现。
-   **[栈 (Stacks)](./data-structures/stacks.md)**：讲解栈的 LIFO (后进先出) 原理及其 Python 实现。
-   **[树 (Trees)](./data-structures/trees.md)**：介绍树的基本概念，重点是二叉树的定义、性质和相关操作。

学习这些基础知识将为后续更深入的 Python 编程和算法学习打下坚实的基础。

