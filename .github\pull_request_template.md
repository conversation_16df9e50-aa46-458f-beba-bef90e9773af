# Pull Request

## 变更类型

<!-- 请在下面的选项中勾选一项 -->

- [ ] 新增文档 (请使用 `.github/PULL_REQUEST_TEMPLATE/new_document.md` 模板，标题格式：`docs: 新增[文档标题]文档`)
- [ ] 站点内容更新 (请使用 `.github/PULL_REQUEST_TEMPLATE/content_update.md` 模板，标题格式：`docs: [更新内容简述]`)
- [ ] 问题修复 (请使用 `.github/PULL_REQUEST_TEMPLATE/bug_fix.md` 模板，标题格式：`fix: [问题简述]`)
- [ ] 功能增强 (请使用 `.github/PULL_REQUEST_TEMPLATE/feature_request.md` 模板，标题格式：`feat: [功能名称]`)
- [ ] 其他 (请使用约定式提交格式，如 `chore:`, `style:`, `refactor:`, `perf:`, `test:` 等)

## 变更概述

<!-- 简要描述本次PR的主要内容 -->

## 相关问题

<!-- 如果本PR解决了某个Issue，请在此处关联 -->
<!-- 例如：Fixes #123 -->

## 变更详情

<!-- 使用约定式提交格式描述变更内容，例如：docs: 更新导航页面，完善内容结构和学习路径 -->
<!-- 详细描述变更内容 -->

## 检查清单

- [ ] 代码/内容符合项目规范
- [ ] 变更已经过充分测试
- [ ] 相关文档已更新

## 其他说明

<!-- 其他需要说明的事项 -->