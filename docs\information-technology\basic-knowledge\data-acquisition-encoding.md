---
title: 数据采集与编码
---

# 数据采集与编码

## 数字化基础

### 数字化定义
数字化的定义：将模拟信号转换为数字信号的过程称为数字化。

### 模拟信号与数字信号
模拟信号是以连续变化的物理量存在的信号；数字信号是在取值上是离散的、不连续的信号。

### 模拟信号转数字信号的过程
将模拟信号转换成数字信号(A/D 转换）一般需要经过采样、量化与编码。

1. **采样**是指将时间上连续的模拟信号在时间轴上离散化的过程，其主要参数是采样频率（每秒的采样次数），单位是赫兹(Hz)。提高采样频率能提高声音或图像的保真度。例如，若某音频的采样频率是44.1kHz,则表示每一秒时间采集音频样本点的个数是44100个。

2. **量化**指将信号的连续取值近似为有限个离散值的过程，即将采样后得到的每个样本的幅度值进行离散化处理。量化主要参数是量化位数，单位是比特(bit)；量化位数越多，能够表示的幅度级别就越多，划分的越精细，量化结果与实际数据也越接近，保真度越高。

## 数据单位与编码

### 容量单位
容量单位"字节"(Byte, B)与"位"(bit, b)的换算关系是：8bit = 1B。

### ASCII码
美国信息交换标准码ASCII码是一种常用的字符编码：
1. 其标准形式用7个二进制位(7bit)表示一个字符，因此最多可以表示2^7 = 128个不同的字符，最大值是127D（十进制）或7FH（十六进制）。在计算机中通常用一个字节（8bit）来存储一个ASCII字符，最高位通常为0。
2. 常见的ASCII码值需要记忆："0"的ASCII码是48D，"A"的ASCII码是65D，"a"的ASCII码是97D。

## 图像与多媒体

### 位图图像与色彩深度
位图图像中，色彩的丰富程度由量化位深度（或称颜色深度）决定。例如，256色的图像，其量化位深度是8bit（因为2^8=256），即每个像素用1B存储颜色信息；24位真彩色的图像，其量化位深度是24bit，即每个像素用3B存储颜色信息。注意区分16色（表示有16种颜色，位深度为4bit）与16位色（表示位深度为16bit，能表示2^16种颜色）。

### 数字媒体文件容量计算
数字媒体文件容量计算公式：
- 图像文件容量 = 像素总量 × 单位像素容量（即位深度/8，单位：字节）
- 声音文件容量 = 采样频率(Hz) × 量化位数(bit) × 声道数 × 时间(秒) / 8 (单位：字节)
- 视频文件容量 = 单帧图像容量(字节) × 总帧数 = 单帧图像容量(字节) × 帧频(fps) × 时间(秒)

### 矢量图形特点
矢量图形的特点是：文件容量通常较小；文件大小与图形的显示尺寸无关；图像在放大或缩小时不会产生锯齿状失真（即无损缩放）；通常用于表示色彩相对简单的图形，如徽标、图表（早期矢量图一般支持256色，但现代矢量格式支持更丰富的色彩）。

## 常见文件格式

### 图像文件格式
常见的图像编码格式 (即图像文件格式) 包括：BMP (未压缩的位图格式)、JPEG (有损压缩格式，适合照片)、GIF (支持动画和透明背景，颜色数较少)、PNG (无损压缩格式，支持透明背景)。

### 音频文件格式
常见的音频编码格式 (即音频文件格式) 包括：WAV (未压缩或轻微压缩，音质好但文件大)、MP3 (有损压缩格式，流行度高)、WMA (Windows Media Audio，微软开发的音频格式)。

### 视频文件格式
常见的视频编码格式 (即视频文件格式) 包括：AVI (较早的视频容器格式)、MP4 (目前广泛使用的视频容器格式，兼容性好)、WMV (Windows Media Video，微软开发的视频格式)、MPEG (一系列视频和音频压缩标准，如MPEG-1, MPEG-2, MPEG-4)。
