---
title: 信息系统、支撑技术与信息安全
---

# 信息系统、支撑技术与信息安全

## 信息技术概览

信息技术是指获取、传输、存储、加工和表达信息的各种技术总和，主要包括计算机技术、网络技术、通信技术。

## 信息系统

### 概念

信息系统是指由硬件软件设施、通信网络、数据和用户构成的人机交互系统。

### 功能

信息系统的功能主要包括：数据收集和输入、存储、传输、加工处理、输出以及查询功能。

### 局限性

信息系统的局限性有：
① 对外部环境有依赖性；
② 本身存在安全隐患；
③ 技术门槛可能加剧数字鸿沟。

## 计算机硬件

计算机硬件主要由运算器、控制器、存储器、输入设备和输出设备五大逻辑部件组成。

### 中央处理器 (CPU)

中央处理器(CPU)由运算器和控制器组成，其性能指标有：主频、字长、核心数量、高速缓存等。
- **龙芯**是我国首款自主研发的计算机中央处理器。
- **麒麟(华为)**是我国首款国产移动终端中央处理器。

### 存储器

存储器可分为主存储器、辅助存储器（如硬盘、U盘）、高速缓存(Cache)。
1.  **主存储器**可以分为随机存取存储器(RAM)和只读存储器(ROM)。
2.  **RAM（随机存取存储器）** 的特点是可读可写，但断电后数据会消失，通常指内存条。
3.  **ROM（只读存储器）** 的特点是通常只能读取不能写入（或写入困难），断电后数据不消失。
4.  **存储器的读写速度**由快到慢一般为：高速缓存 > 内存(RAM) > 硬盘(辅助存储器)；而**存储容量**由大到小一般是：硬盘 > 内存 > 高速缓存。

## 移动终端

移动终端的体系结构和工作原理与计算机基本相同，也包括输入、处理(运算与控制)、存储、输出四个主要部分。

## 计算模型与体系结构

现代计算机的计算模型是**图灵机**。现代计算机普遍采用“**存储程序式**”（也称冯·诺依曼式）体系结构，其核心思想是程序和数据都以二进制形式存储在存储器中，计算机能自动执行指令。

## 软件

软件是用户与硬件之间的接口，可分为系统软件和应用软件。
- **系统软件**主要包括：操作系统、计算机语言编译器（或解释器）、数据库管理系统、驱动程序等。
- **常见的计算机操作系统**有：Windows、Linux(开源)、Mac OS、Unix。
- **移动终端常见的操作系统**有：Android(基于Linux内核)、iOS(苹果公司)、Windows (移动版)、HarmonyOS(鸿蒙OS/华为)等。

## 移动终端特性与传感器

移动终端具有**移动性**和**智能性**两大特性。“智能性”主要基于各种传感器的植入，常见的传感器及其功能包括：
1.  **光线传感器**：用于自动调节屏幕背光的亮度，以适应环境光线变化。
2.  **距离传感器**：用于检测手机是否贴近耳朵打电话，以便自动调暗或关闭屏幕，防止误触并节省电量。
3.  **重力传感器**：用于实现手机横竖屏智能切换、拍照时自动判断照片朝向等功能。
4.  **加速度传感器**：用于计步、检测睡眠状态（如翻身次数）等运动相关的应用。
5.  **指纹传感器**：用于设备加密、解锁、以及进行安全的电子支付身份验证。
6.  **霍尔传感器**：常用于配合磁性保护套，实现翻盖自动解锁屏幕、合盖自动锁屏的功能。

## 传感技术与控制技术

- **传感技术**负责将采集到的外部物理、化学或生物等信息输入到信息系统中。
- **控制技术**则负责实现信息系统对外部设备或过程的控制。
- **传感器**属于信息输入设备，是一种能够感受被测量（如温度、压力、光线等）并按照一定的规律将其转换成可用输出信号（通常是电信号）的器件或装置，一般由敏感元件（直接感受被测量）、转换元件（将敏感元件的输出转换为电信号）和其他辅助元件三部分组成。

## 射频识别技术 (RFID)

射频识别技术（Radio Frequency Identification，简称RFID）是一种无线通信技术，至少需要两大基本元素：发射端（通常是电子标签，Tag）和接收端（通常是读写器，Reader/Writer）。射频识别技术也属于传感技术的一种。
- **电子标签**根据其能量获取方式（即有无内置电池）可分为有源标签（自带电池，主动发送信号）和无源标签（无内置电池，依靠读写器发射的射频能量激活并发送信号）。
- **RFID系统工作流程**一般是：读写器天线发射特定频率的射频信号；当电子标签进入读写器的工作范围时，如果是无源标签，则从射频信号中获取能量被激活，其内置芯片向外发送存储的电子编码信息；读写器通过天线接收电子标签发送的信息，经过解码后传输到后端的信息系统进行处理。

## 近场通信技术 (NFC)

近场通信技术（Near Field Communication，简称NFC）是由RFID技术演变而来的一种短距离（通常运行于10厘米以内）高频无线识别和数据交换技术。例如，银行卡具备的“闪付”功能，就是在银行卡上集成了支持NFC功能的芯片，允许在近距离内进行非接触式支付。

## 网络功能与类型

### 网络功能

网络的三个主要功能是：数据通信功能（实现信息在不同节点间的传递）、资源共享功能（如共享文件、打印机、数据库等软硬件资源）、分布处理功能（将大型任务分解到网络中多台计算机协同完成）。

### 主要网络类型

当前社会影响力较大的三大网络是：计算机网络（如互联网）、移动通信网络（如4G/5G网络）、广播电视网络。

### 计算机网络分类

计算机网络按其覆盖的地理范围可分为：
- **局域网(LAN, Local Area Network)**，如家庭网络、校园网；
- **城域网(MAN, Metropolitan Area Network)**，覆盖一个城市的网络；
- **广域网(WAN, Wide Area Network)**，覆盖国家或全球范围的网络，如互联网。

## 计算机网络组成

计算机网络通常由三部分组成：计算机系统、数据通信系统、以及网络软件和网络协议。
1.  **计算机系统**是网络中的节点，主要用于完成信息的收集、存储、处理和输出等任务，并提供各种网络资源。根据其在网络中的用途，可以分为服务器（提供服务和资源）和终端（用户设备）。
2.  **数据通信系统**主要由传输介质（如双绞线、光纤、无线电波）和网络互联设备（如交换机、路由器、网卡）组成，负责数据的实际传输。
3.  **网络软件和网络协议**是使网络中不同终端、不同网络之间能够相互识别和正确通信的一组标准及规则，是计算机网络正常工作的基础。在Internet上进行信息传输，至少会涉及到多个层次的协议，例如：
    *   **TCP协议（传输控制协议）**：负责管理被传送内容的完整性和可靠性。
    *   **IP协议（网际协议）**：负责将信息包从源地址传送到目的地址（路由选择和寻址）。
    *   **应用层协议（AP协议是一个泛指，具体如HTTP）**：将传输的信息转换为人类或应用程序能识别和使用的内容。
    *   **常见的网络协议还有**：HTTP（超文本传输协议），用于Web浏览；FTP（文件传输协议），用于文件上传下载；HTML（超文本标记语言），用于构建网页内容；DHCP（动态主机配置协议），用于自动分配IP地址等网络配置信息；PPPoE（基于以太网的点对点协议），常用于宽带拨号上网；DNS（域名系统），负责将易记的域名解析为IP地址。

## 网络应用软件架构

网络应用软件的实现框架主要有：客户端/服务器架构(C/S架构)和浏览器/服务器架构(B/S架构)。
1.  **B/S架构的特点**是：客户端通常是通用的浏览器，大部分应用逻辑和数据处理在服务器端完成，因此服务器的负荷较重，对服务器性能要求较高，通信开销也可能较高。其优点是客户端无需安装特定软件，升级和维护主要在服务器端进行，方便快捷，极大地降低了成本和工作量。
2.  **C/S架构的特点**是：客户端需要安装专门的应用程序，可以将一部分任务合理地分配到客户端和服务器端共同处理，从而可以降低系统的通信开销和服务器压力，也可能降低某些情况下的开发难度（例如利用客户端本地计算能力）。其缺点是客户端软件必须预先安装，给应用程序的升级和维护带来一定的困难。

## 密码系统

密码系统是实现信息加密和解密的技术体系，包括明文（原始信息）、密文（加密后的信息）、密钥（控制加密和解密过程的参数）和密码算法（加密算法和解密算法）四个核心方面。密码系统的安全性主要由密钥的保密性和强度以及算法的健壮性共同决定，但通常强调密钥的决定性作用，因为算法往往是公开的。

### 常见加密方法类型

1.  **替代密码**：是将明文中每个位置的字符用其他字符（根据一定的规则或密钥）替代来实现加密，如凯撒密码。
2.  **换位密码（也称置换密码）**：是将明文中原有的字符顺序通过一定的规则（由密钥决定）重新排列，字符本身不变，但位置改变。
3.  **异或加密**：是一种简单的对称加密方法，将明文的二进制表示与密钥的二进制表示进行按位异或运算得到密文，解密时用相同的密钥对密文再进行一次异或运算即可恢复明文。
4.  **根据加密密钥和解密密钥是否相同，密码体制可分为**：
    *   **对称加密**（加密和解密使用相同的密钥，或解密密钥可由加密密钥轻易推导出）
    *   **非对称加密**（加密和解密使用不同的密钥，即公钥和私钥，公钥加密的内容只能用对应的私钥解密，反之亦然）。

## 身份认证与访问控制

- **身份认证（Authentication）** 要解决的问题是确认用户是否是其所声称的合法用户，即验证用户身份的真实性，判断用户是否有权限进入系统或访问受保护的资源。
- **访问控制（Authorization）** 则是在用户身份被成功认证后，要解决的问题是该合法用户对系统中的数据或资源拥有哪些操作权限（如读取、写入、删除、执行等）。

### 常见身份认证技术

1.  **用户名+口令（密码）的认证技术**：是最常见的认证方式，主要包括静态口令（用户设定的固定密码）和动态口令（如通过令牌、短信验证码等方式生成的一次性或有时效性的密码）。
2.  **依靠生物特征识别的认证技术**：利用人体固有的生理特征或行为特征进行身份验证，例如指纹识别、语音识别、人脸识别、虹膜识别等。
3.  **USB Key认证技术**：是一种硬件认证方式，用户需要插入USB Key（一种包含加密芯片的USB设备）并可能需要输入PIN码，采用软硬件相结合、通常实现一次一密的强认证模式。

## 防火墙技术

防火墙技术是一种网络安全技术，通常是兼有软件和硬件设备的系统，部署在内部网络（如企业内网）与外部网络（如互联网）之间，用于隔离内网和外网，根据预设的安全策略控制进出网络的通信，防止外网对内网进行未授权的访问和攻击。防火墙主要由服务访问规则（定义哪些服务和流量被允许或禁止）、验证工具（如用户认证）、包过滤（检查网络数据包的头部信息）和应用网关（对特定应用层协议进行深度检查和控制）等部分组成。

## 计算机病毒

计算机病毒是指人为编制的、能够实现自我复制并潜伏在计算机系统中，当特定条件满足时被激活，从而对计算机系统或数据造成干扰或破坏的一段计算机指令或者程序代码。它具有传染性（能通过各种途径传播并感染其他程序或系统）、寄生性（依附于其他宿主程序或文件）、隐蔽性（不易被察觉）、潜伏性（在特定条件下才发作）、破坏性（可能损坏数据、程序或硬件）、可触发性（由特定事件或时间触发）等特征。病毒防治的基本策略是：预防为主，查杀为辅。

## 漏洞

漏洞（Vulnerability）是指信息系统（包括硬件、软件、协议或管理等方面）存在的弱点或缺陷，这些缺陷可能被攻击者利用来执行未授权的操作、获取敏感信息、破坏系统完整性或可用性等。漏洞的防治手段主要包括：使用防火墙技术阻挡部分攻击；经常使用安全监测与扫描工具来发现系统中的安全漏洞及薄弱环节；及时安装系统和应用程序的安全补丁，经常升级系统和安全软件；定期进行数据备份和系统备份，以便在发生安全事件时恢复。

## 信息系统搭建准备

搭建信息系统的前期准备工作主要包括：需求分析（明确系统的功能、性能等用户需求）、可行性分析（从技术、经济、操作等方面评估项目的可行性）、开发模式的选择（如瀑布模型、敏捷开发等）、概要设计（规划系统的整体架构、模块划分、主要功能流程）、详细设计（对每个模块进行具体的功能和接口设计）。

## 系统测试

系统测试是确保信息系统质量的重要环节，通常包括软件测试、硬件测试、网络测试等。其中，软件测试的方法主要包括正确性证明（理论上证明程序符合规格）、静态测试（不实际运行程序，通过代码审查、文档评审等方式检查）和动态测试（实际运行程序，输入测试用例，检查输出结果是否符合预期）。**（特别注意：只有动态测试才需要实际运行程序）**
