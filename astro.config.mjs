import { defineConfig } from 'astro/config';
import starlight from '@astrojs/starlight';

// https://astro.build/config
export default defineConfig({
  integrations: [
    starlight({
      title: '浙江省杭州高级中学 技术学科知识库',
      description: '本知识库服务于浙江省杭州高级中学选考技术学科的学生，包含信息技术和通用技术两部分内容，并优先专注于信息技术知识的梳理。',
      logo: {
        light: './src/assets/hgbanner-light.webp',
        dark: './src/assets/hgbanner-dark.webp',
      },
      // favicon: '/favicon.ico',
      social: {
        // 可以在这里添加社交链接
      },
      sidebar: [
        {
          label: '指南',
          items: [
            { label: '知识库导航', link: '/guide/' },
          ],
        },
        {
          label: '信息技术基础知识',
          items: [
            { 
              label: '信息系统、支撑技术与信息安全', 
              link: '/information-technology/basic-knowledge/information-systems-support-security/' 
            },
            { 
              label: '数据采集与编码', 
              link: '/information-technology/basic-knowledge/data-acquisition-encoding/' 
            },
            { 
              label: '数据、信息、大数据及人工智能', 
              link: '/information-technology/basic-knowledge/data-information-bigdata-ai/' 
            },
          ],
        },
        {
          label: '算法',
          items: [
            { label: '算法概览', link: '/information-technology/algorithms/' },
            {
              label: '算法基础概念与效率',
              link: '/information-technology/algorithms/algorithm-concepts/efficiency-and-concepts/',
            },
            {
              label: '基本算法',
              items: [
                {
                  label: '迭代算法',
                  link: '/information-technology/algorithms/basic-algorithms/iterative-algorithm/',
                },
                {
                  label: '递归算法',
                  link: '/information-technology/algorithms/basic-algorithms/recursive-algorithm/',
                },
              ],
            },
            {
              label: '查找算法',
              items: [
                {
                  label: '顺序查找',
                  link: '/information-technology/algorithms/searching/sequential-search/',
                },
                {
                  label: '二分查找（对分查找）',
                  link: '/information-technology/algorithms/searching/binary-search/',
                },
              ],
            },
            {
              label: '排序算法',
              items: [
                {
                  label: '冒泡排序',
                  link: '/information-technology/algorithms/sorting/bubble-sort/',
                },
                {
                  label: '选择排序',
                  link: '/information-technology/algorithms/sorting/selection-sort/',
                },
                {
                  label: '插入排序',
                  link: '/information-technology/algorithms/sorting/insertion-sort/',
                },
              ],
            },
          ],
        },
        {
          label: 'Python 基础知识',
          items: [
            {
              label: 'Python 基础知识概览',
              link: '/information-technology/programming-languages/python/',
            },
            {
              label: 'Python 基础知识字典',
              link: '/information-technology/programming-languages/python/python-basic-dictionary/',
            },
            {
              label: 'Pandas 与 Matplotlib',
              link: '/information-technology/programming-languages/python/python-pandas-matplotlib/',
            },
            {
              label: '数组',
              link: '/information-technology/programming-languages/python/data-structures/arrays/',
            },
            {
              label: '字符串',
              link: '/information-technology/programming-languages/python/data-structures/strings/',
            },
            {
              label: '队列',
              link: '/information-technology/programming-languages/python/data-structures/queues/',
            },
            {
              label: '栈',
              link: '/information-technology/programming-languages/python/data-structures/stacks/',
            },
            {
              label: '树',
              link: '/information-technology/programming-languages/python/data-structures/trees/',
            },
          ],
        },
        {
          label: '通用技术',
          items: [
            { label: '通用技术概览', link: '/general-technology/' },
          ],
        },
        {
          label: 'AI 智能助手',
          items: [
            { label: 'AI 智能助手', link: '/ai-assistant/' },
          ],
        },
      ],
      customCss: [
        // 可以在这里添加自定义CSS
      ],
    }),
  ],
});
